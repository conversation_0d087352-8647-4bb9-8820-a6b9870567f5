import { useEffect, useState } from "react"
import { Link, useParams } from "react-router-dom"
import useProductDetails from "../../../react-query/hooks/product-hooks/useProductDetails"
import ProductDetailsImg from "../../../components/product/product-details/product-details-img/ProductDetailsImg"
import ProductDetailsInfo from "../../../components/product/product-details/product-details-info/ProductDetailsInfo"
import cartStore from "../../../zustand/cartStore"
import Specifications from "../../../components/utils/specs/Specifications"
// import { MdKeyboardDoubleArrowDown, MdKeyboardDoubleArrowUp } from "react-icons/md"
import styles from "./ProductDetails.module.scss"
import Reviews from "../../../components/reviews/Reviews"
import Spinner from "../../../components/utils/spinner/Spinner"
import Alert from "../../../components/utils/alert/Alert"
import { ProductVariant } from "../../../types/product-types"
import { findFirstAvailableImage, findVariantImage } from "../../../utils/product-utils"

const ProductDetails = () => {
  const { slug } = useParams()

  // const [showFullDescription, setShowFullDescription] = useState(false)
  const [selectedImage, setSelectedImage] = useState<string>('')
  const [qty, setQty] = useState(1)
  const [activeTab, setActiveTab] = useState<'description' | 'specifications' | 'reviews'>('description')

  const { setProductVariant, resetExtraData, selectedVariant } = cartStore()

  const { isPending, error, data: product } = useProductDetails(slug!)

  useEffect(() => {
    if (!isPending && !error && product) {
      // Always set the variant with the lowest order value (first in display order)
      if (product.product_variant && product.product_variant.length > 0) {
        // Sort variants by order and select the first one
        const sortedVariants = [...product.product_variant].sort((a, b) => a.order - b.order)
        setProductVariant(sortedVariants[0])
      }

      // Find the first available image across all variants
      const imageUrl = findFirstAvailableImage(product, import.meta.env.VITE_CLOUDINARY_URL)
      if (imageUrl) {
        setSelectedImage(imageUrl)
      } else {
        // If no image is found in any variant, set a placeholder
        // The ProductDetailsImg component will handle showing a placeholder
        setSelectedImage('placeholder')
      }
    }
  }, [isPending, error, product, setProductVariant])

  useEffect(() => {
    // Reset productExtraData when productId changes
    resetExtraData()
  }, [slug, resetExtraData])

  // Update selectedImage when selectedVariant changes
  useEffect(() => {
    if (selectedVariant && product) {
      const imageUrl = findVariantImage(selectedVariant, product, import.meta.env.VITE_CLOUDINARY_URL)
      if (imageUrl) {
        setSelectedImage(imageUrl)
      } else {
        // If no image is found, set a placeholder
        setSelectedImage('placeholder')
      }
    }
  }, [selectedVariant, product])


  const handleImageClick = (imageSrc: string) => {
    setSelectedImage(imageSrc)
  }

  const handleVariantClick = (variant: ProductVariant) => {
    setProductVariant(variant)
  }

  // // Function to handle quantity change, restricting it between 1 and 10
  const handleQtyChange = (newQty: number) => {
    // Check if the newQty is a valid number between 1 and 10
    if (!isNaN(newQty) && newQty >= 1 && newQty <= 10) {
      // Update the quantity state if the newQty is valid
      setQty(newQty)
    }
  }

  return (
    <div className="container">
      {isPending ? <Spinner color='#0091CF' size={20} loading={true} /> :
        error ? <Alert variant='error' message={error.message} /> : <>
          <div className={styles.breadcrumbs}>
            <Link to="/">Home</Link> &gt;{' '}
            {product?.category && (
              <>
                <Link to={`/category/${product.category.slug}`}>{product.category.title}</Link> &gt;{' '}
              </>
            )}
            <span>{product?.title}</span>
          </div>

          <section className={styles.product_details}>
            {/* <div className={styles.product_details_left}> */}
            <ProductDetailsImg
              selectedImage={selectedImage}
              product={product}
              handleImageClick={handleImageClick}
              selectedVariant={selectedVariant || undefined}
            />
            {/* </div> */}

            {/* <div className={styles.product_details_right}> */}
            <ProductDetailsInfo
              product={product}
              handleVariantClick={handleVariantClick}
              handleQtyChange={handleQtyChange}
              qty={qty}
              selectedImage={selectedImage}
            />
            {/* </div> */}
          </section>

          <section className={styles.product_tabs}>
            <div className={styles.tabs_header}>
              <button
                className={activeTab === 'description' ? styles.active : ''}
                onClick={() => setActiveTab('description')}
              >
                Description
              </button>
              <button
                className={activeTab === 'specifications' ? styles.active : ''}
                onClick={() => setActiveTab('specifications')}
              >
                Specifications
              </button>
              <button
                className={activeTab === 'reviews' ? styles.active : ''}
                onClick={() => setActiveTab('reviews')}
              >
                Reviews {product?.reviews.length > 0 && `(${product.reviews.length})`}
              </button>
            </div>

            <div className={styles.tabs_content}>

              {activeTab === 'description' && (
                <div className={styles.product_description}>
                  <h3>About this item:</h3>
                  {product?.description && (
                    <p>{product.description}</p>
                  )}
                </div>
              )}

              {activeTab === 'specifications' && (
                <div className={styles.specifications}>
                  <h3>Product information:</h3>
                  <Specifications
                    selectedVariant={selectedVariant}
                  />
                </div>
              )}

              {activeTab === 'reviews' && (
                <div className={styles.reviews}>
                  <h3>Customer reviews:</h3>
                  {product?.reviews.length === 0 ? (
                    <p>No reviews yet</p>
                  ) : (
                    <Reviews reviews={product?.reviews} slug={slug!} />
                  )}
                </div>
              )}
            </div>
          </section>
        </>}
    </div>
  )
}

export default ProductDetails